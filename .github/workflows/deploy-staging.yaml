name: Deploying to Staging
on:
  push:
    branches:
      - staging

jobs:
  sonarqube:
    name: Sonar<PERSON><PERSON> Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0 
      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      - name: SonarQube Quality Gate
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  build:
    needs: sonarqube  # Ensures build starts only after SonarQube scan completes
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      
      - name: Login to SWR
        uses: huaweicloud/swr-login@v2.1.0
        with:
          access-key-id: ${{ secrets.ACCESS_KEY_ID }}
          access-key-secret: ${{ secrets.ACCESS_KEY_SECRET }}
          region: af-south-1

      - name: Build and push Docker image
        run: |
          docker build -t swr.af-south-1.myhuaweicloud.com/credpal-prod/crypto-service:staging .
          docker push swr.af-south-1.myhuaweicloud.com/credpal-prod/crypto-service:staging  

  deploy:
    needs: build  # Ensures deploy runs only after build completes
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Coolify
        env:
          COOLIFY_API_TOKEN: ${{ secrets.COOLIFY_TOKEN }}
          COOLIFY_URL: ${{ secrets.COOLIFY_URL }}
          COOLIFY_APP_ID: crypto-service:staging
        run: |
          curl $COOLIFY_URL/api/v1/deploy?tag=$COOLIFY_APP_ID \
            -H "Authorization: Bearer $COOLIFY_API_TOKEN" \
            -H "Content-Type: application/json" 
        if: ${{ always() }} # Use always to ensure that the notification is also sent on failure of previous steps

      - name: Notify Google space 
        uses: SimonScholz/google-chat-action@main
        with:
          webhookUrl: '${{ secrets.GOOGLE_CHAT_WEBHOOK_URL }}'
          title: Deployment
          jobStatus: '${{ job.status }}'
