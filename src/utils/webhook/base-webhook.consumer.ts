import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RabbitmqService } from '@crednet/utils';
import { Exchanges } from '../queue';

/**
 * Base class for all webhook consumers
 * Provides common functionality for subscribing to events and handling errors
 */
@Injectable()
export abstract class BaseWebhookConsumer implements OnModuleInit {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(protected readonly rmqService: RabbitmqService) {}

  /**
   * Subscribe to events when the module initializes
   */
  async onModuleInit(): Promise<void> {
    this.registerEventHandlers();
  }

  /**
   * Register event handlers for the events this consumer is interested in
   * This method should be implemented by subclasses
   */
  protected abstract registerEventHandlers(): void;

  /**
   * Subscribe to an event
   * @param eventName The event to subscribe to
   */
  protected subscribe(eventName: string): void {
    this.rmqService.subscribe(
      `${Exchanges.WEBHOOK}.${eventName}`,
      this.handleWebhookEvent.bind(this),
    );
    this.logger.log(`Subscribed to event: ${eventName}`);
  }

  /**
   * Handle a webhook event
   * This method is called when an event is received
   * @param param0 The event data
   */
  protected async handleWebhookEvent({
    message,
    data,
    ack,
    // reject,
  }): Promise<void> {
    try {
      const routingKey = message.fields.routingKey;
      const eventName = routingKey.split('.').pop();

      this.logger.debug(`Handling webhook event: ${eventName}`);

      const handler = this.getEventHandler(eventName);

      if (handler) {
        await handler(data);
        this.logger.debug(`Successfully processed event: ${eventName}`);
        ack();
      } else {
        this.logger.warn(`No handler found for event: ${eventName}`);
        ack(); // Acknowledge the message even if we don't have a handler
      }
    } catch (error) {
      this.logger.error(
        `Error handling webhook event: ${error.message}`,
        error.stack,
      );
      // We acknowledge the message to prevent it from being requeued
      // In a production environment, you might want to use reject() or implement a dead-letter queue
      ack();
    }
  }

  /**
   * Get the handler for an event
   * This method should be implemented by subclasses
   * @param eventName The event name
   * @returns A function that handles the event
   */
  protected abstract getEventHandler(
    eventName: string,
  ): (data: any) => Promise<void>;
}
