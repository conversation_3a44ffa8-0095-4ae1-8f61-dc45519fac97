import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPortfolioTables1630000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE portfolio_snapshots (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        total_value DECIMAL(18, 8) NOT NULL,
        asset_distribution JSON NOT NULL,
        timestamp TIMESTAMP NOT NULL,
        change_percentage_24h DECIMAL(10, 2),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    await queryRunner.query(`
      CREATE TABLE whitelisted_addresses (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        address VARCHAR(255) NOT NULL,
        label VARCHAR(255) NOT NULL,
        currency VARCHAR(10) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    await queryRunner.query(`
      CREATE TABLE recurring_buys (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        amount DECIMAL(18, 8) NOT NULL,
        currency VARCHAR(10) NOT NULL,
        frequency VARCHAR(20) NOT NULL,
        next_execution_date TIMESTAMP NOT NULL,
        last_execution_date TIMESTAMP NULL,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE recurring_buys;`);
    await queryRunner.query(`DROP TABLE whitelisted_addresses;`);
    await queryRunner.query(`DROP TABLE portfolio_snapshots;`);
  }
}