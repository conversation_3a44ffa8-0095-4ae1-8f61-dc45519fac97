import { Injectable, Logger } from '@nestjs/common';
import { QuidaxService } from '@app/quidax';
import { DcaStrategyRepository } from '../repositories/dca-strategy.repository';
import { UserRepository } from '../repositories/users.repository';
import { Cron, CronExpression } from '@nestjs/schedule';
import { randomUUID } from 'crypto';
import { DcaStrategy, DcaFrequency } from '../entities/dca-strategy.entity';

@Injectable()
export class DcaService {
  private readonly logger = new Logger(DcaService.name);

  constructor(
    private readonly dcaStrategyRepository: DcaStrategyRepository,
    private readonly quidaxService: QuidaxService,
    private readonly userRepository: UserRepository,
  ) {}

  async createDcaStrategy(userId: string, dcaStrategyDto: any) {
    const user = await this.userRepository.getUserByUserId(userId);

    const nextExecutionDate = this.calculateNextExecutionDate(
      dcaStrategyDto.frequency,
    );

    return this.dcaStrategyRepository.save({
      user,
      currency: dcaStrategyDto.currency,
      amount: dcaStrategyDto.amount,
      frequency: dcaStrategyDto.frequency,
      nextExecutionDate,
      totalInvested: '0',
      totalTokensAcquired: '0',
      executionCount: 0,
    });
  }

  @Cron(CronExpression.EVERY_HOUR)
  async executeDcaStrategies() {
    const dueStrategies = await this.dcaStrategyRepository.findDueStrategies();

    for (const strategy of dueStrategies) {
      try {
        const amountToInvest = strategy.amount;

        // Execute the buy order
        const reference = 'cp_dca_' + randomUUID();
        const orderResult = await this.quidaxService.createOrder(
          strategy.user.id,
          {
            market: strategy.currency + 'ngn',
            side: 'buy',
            volume: amountToInvest,
            ord_type: 'market',
            reference,
          },
        );

        if (orderResult.status === 'success') {
          // Calculate next execution date
          const nextExecutionDate = this.calculateNextExecutionDate(
            strategy.frequency,
          );

          // Update strategy execution stats
          await this.dcaStrategyRepository.updateExecutionStats(
            strategy.id,
            amountToInvest,
            orderResult.data?.executed_volume?.amount || '0',
            nextExecutionDate,
          );

          this.logger.log(
            `DCA strategy executed for user ${strategy.user.id}: ${amountToInvest} ${strategy.currency}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to execute DCA strategy for user ${strategy.user.id}:`,
          error,
        );
      }
    }
  }

  private calculateNextExecutionDate(
    frequency: DcaFrequency,
    fromDate = new Date(),
  ): Date {
    const nextDate = new Date(fromDate);

    switch (frequency) {
      case DcaFrequency.DAILY:
        nextDate.setDate(nextDate.getDate() + 1);
        break;
      case DcaFrequency.WEEKLY:
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case DcaFrequency.MONTHLY:
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      default:
        nextDate.setDate(nextDate.getDate() + 1);
    }

    return nextDate;
  }
}
