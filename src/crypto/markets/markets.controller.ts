import { Controller, Get, Param } from '@nestjs/common';
import { MarketsService } from './markets.service';
import { Market } from '../entities/markets.entity';

@Controller('markets')
export class MarketsController {
  constructor(private readonly cryptoMarketsService: MarketsService) {}

  @Get('/')
  async getMarkets(): Promise<any> {
    return this.cryptoMarketsService.getMarkets();
  }

  @Get('/simple')
  async getMarketsSimple(): Promise<any> {
    return this.cryptoMarketsService.getMarketsSimple();
  }

  @Get('/:id')
  async getMarketById(@Param('id') id: string): Promise<{
    market: Market;
    marketData: any;
  }> {
    return this.cryptoMarketsService.getMarketById(id);
  }
}
