import { Module } from '@nestjs/common';
import { MarketsService } from './markets.service';
import { MarketsController } from './markets.controller';
import { MarketRepository } from '../repositories/market.repository';
import { QuidaxModule } from '@app/quidax';
import { MarketCron } from './markets.cron';

@Module({
  imports: [QuidaxModule],
  controllers: [MarketsController],
  providers: [MarketsService, MarketRepository, MarketCron],
})
export class MarketsModule {}
