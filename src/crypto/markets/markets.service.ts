import { Injectable } from '@nestjs/common';
import { QuidaxService } from '@app/quidax';
import { MarketRepository } from '../repositories/market.repository';
import { MarketDataDto } from '../dtos/market.dto';
import { RedisService } from '@crednet/utils';

@Injectable()
export class MarketsService {
  constructor(
    private readonly quidaxService: QuidaxService,
    private readonly marketRepository: MarketRepository,
    private readonly redisService: RedisService,
  ) {}

  async createMarkets(): Promise<void> {
    try {
      const getMarkets = await this.quidaxService.getCryptoMarkets();

      const markets: MarketDataDto[] = getMarkets.map((market) => ({
        id: market.id,
        name: market.name,
        baseUnit: market.base_unit,
        quoteUnit: market.quote_unit,
        priceStep: market.filters.price_step,
        basePrecision: market.trading_rules.base_precision,
        quotePrecision: market.trading_rules.quote_precision,
        pricePrecision: market.trading_rules.price_precision,
        minimumOrderSize: market.trading_rules.minimum_order_size,
      }));

      await this.marketRepository.createOrUpdateMarkets(markets);
    } catch (error) {
      console.error(error);
    }
  }

  async getMarkets() {
    const markets = await this.marketRepository.getMarkets();

    const storedData = [];
    for (const market of markets) {
      const data = await this.redisService
        .getClient()
        .HGETALL(`market:data:${market.id}`);
      storedData.push({ ...market, ...data });
    }

    return storedData;
  }

  async getMarketById(id: string) {
    const market = await this.marketRepository.getMarketById(id);

    const data = await this.redisService
      .getClient()
      .HGETALL(`market:data:${id}`);

    return { market, marketData: data };
  }

  async getMarketsSimple() {
    return await this.marketRepository.getMarketsByQuotes();
  }
}
