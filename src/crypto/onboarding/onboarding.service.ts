import { Injectable } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxService } from '@app/quidax';
import { AuthData } from '@crednet/authmanager';

@Injectable()
export class OnboardingService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly quidaxService: QuidaxService,
  ) {}

  async getOnboardingStatus(auth: AuthData) {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());

    return {
      isRegistered: !!user,
      hasCompletedKyc: user?.kycVerified || false,
      hasCreatedWallet: user?.hasWallet || false,
      hasCompletedFirstTransaction: user?.hasTransaction || false,
    };
  }

  async completeOnboardingStep(auth: AuthData, step: string, data: any) {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());

    switch (step) {
      case 'create_wallet':
        // Create default wallets for popular cryptocurrencies
        await this.createDefaultWallets(user.id);
        break;
      case 'investment_preferences':
        // Save user investment preferences
        await this.saveInvestmentPreferences(user, data);
        break;
      // Other onboarding steps
    }

    return this.getOnboardingStatus(auth);
  }

  private async createDefaultWallets(userId: string) {
    const defaultCurrencies = ['btc', 'eth', 'usdt'];

    for (const currency of defaultCurrencies) {
      await this.quidaxService.fetchWallet(userId, currency);
    }

    await this.userRepository.update({ id: userId }, { hasWallet: true });
  }

  async getOnboardingSteps(auth: AuthData) {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());

    return {
      steps: [
        {
          id: 'kyc_verification',
          name: 'Identity Verification',
          completed: user.kycVerified,
        },
        { id: 'wallet_setup', name: 'Wallet Setup', completed: user.hasWallet },
        {
          id: 'first_transaction',
          name: 'First Transaction',
          completed: user.hasTransaction,
        },
        {
          id: 'investment_preferences',
          name: 'Investment Preferences',
          completed: !!user.riskProfile,
        },
      ],
      currentStep: this.getCurrentStep(user),
      completionPercentage: this.calculateCompletionPercentage(user),
    };
  }

  private getCurrentStep(user: any): string {
    if (!user.kycVerified) return 'kyc_verification';
    if (!user.hasWallet) return 'wallet_setup';
    if (!user.hasTransaction) return 'first_transaction';
    if (!user.riskProfile) return 'investment_preferences';
    return 'completed';
  }

  private calculateCompletionPercentage(user: any): number {
    let completed = 0;
    if (user.kycVerified) completed++;
    if (user.hasWallet) completed++;
    if (user.hasTransaction) completed++;
    if (user.riskProfile) completed++;
    return (completed / 4) * 100;
  }

  private async saveInvestmentPreferences(user: any, data: any) {
    // Save investment preferences to user profile
    await this.userRepository.update(
      { id: user.id },
      {
        riskProfile: data.riskProfile,
        riskScore: data.riskScore,
      },
    );
  }
}
