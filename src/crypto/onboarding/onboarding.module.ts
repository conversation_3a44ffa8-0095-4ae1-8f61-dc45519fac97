import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OnboardingController } from './onboarding.controller';
import { OnboardingService } from './onboarding.service';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxModule } from '@app/quidax';
import { User } from '../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    QuidaxModule,
  ],
  controllers: [OnboardingController],
  providers: [
    OnboardingService,
    UserRepository,
  ],
  exports: [OnboardingService],
})
export class OnboardingModule {}
