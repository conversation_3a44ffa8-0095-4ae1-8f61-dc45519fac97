import { ApiProperty } from '@nestjs/swagger';
import { OrderSide, OrderType, OrderStatus } from '../entities/orders.entity';
import {
  IsDateString,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
//todo ensure that all the strings dtos are numbers which would later be stringified
export class CreateOrderDto {
  @ApiProperty()
  @IsString()
  baseCurrency: string;

  @ApiProperty()
  @IsString()
  quoteCurrency: string;

  @ApiProperty()
  @IsString()
  side: 'buy' | 'sell';

  @ApiProperty()
  @IsString()
  order_type: 'limit' | 'market';

  @ApiProperty()
  @IsString()
  price?: string;

  @ApiProperty()
  @IsString()
  volume: string;
}

export class OrderDto {
  @IsString()
  id: string;

  @IsString()
  userId: string;

  @IsString()
  reference: string;

  @IsOptional()
  @IsString()
  market_id?: string;

  @IsOptional()
  @IsString()
  base_unit?: string;

  @IsOptional()
  @IsString()
  quote_unit?: string;

  @IsEnum(OrderSide)
  side: OrderSide;

  @IsEnum(OrderType)
  order_type: OrderType;

  @IsString()
  price_unit: string;

  @IsString()
  price_amount: string;

  @IsString()
  avg_price_unit: string;

  @IsOptional()
  @IsString()
  avg_price_amount?: string;

  @IsOptional()
  @IsString()
  volume_unit?: string;

  @IsString()
  volume_amount: string;

  @IsOptional()
  @IsString()
  origin_volume_unit?: string;

  @IsOptional()
  @IsString()
  origin_volume_amount?: string;

  @IsOptional()
  @IsString()
  executed_volume_unit?: string;

  @IsOptional()
  @IsString()
  executed_volume_amount?: string;

  @IsEnum(OrderStatus)
  status: OrderStatus;

  @IsOptional()
  @IsString()
  trades_count?: string;

  @IsOptional()
  meta?: Record<string, any>;

  @IsOptional()
  errors?: Record<string, any>;
}

export class GetOrdersByUserIdDto {
  @ApiProperty()
  @IsDateString()
  startDate: string;

  @ApiProperty()
  @IsDateString()
  endDate: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;
}
