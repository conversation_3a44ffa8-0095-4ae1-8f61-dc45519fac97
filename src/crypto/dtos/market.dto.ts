import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class MarketDataDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty({ description: 'Market name (e.g., BTC_USDT)' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Base unit of the trading pair' })
  @IsString()
  baseUnit: string;

  @ApiProperty({ description: 'Quote unit of the trading pair' })
  @IsString()
  quoteUnit: string;

  @ApiProperty({ description: 'Market price step' })
  @IsNumber()
  @IsOptional()
  priceStep: number;

  @ApiProperty({ description: 'Base precision' })
  @IsNumber()
  basePrecision: number;

  @ApiProperty({ description: 'Quote precision' })
  @IsNumber()
  quotePrecision: number;

  @ApiProperty({ description: 'Price precision' })
  @IsNumber()
  pricePrecision: number;

  @ApiProperty({ description: 'Minimum order size' })
  @IsNumber()
  minimumOrderSize: number;
}
