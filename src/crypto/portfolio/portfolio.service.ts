import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { AuthData } from '@crednet/authmanager';
import { UserRepository } from '../repositories/users.repository';
import { PortfolioRepository } from '../repositories/portfolio.repository';
import { Wallet } from '../entities/wallet.entity';
import {
  TransactionType,
  TransactionStatus,
} from '../entities/transactions.entity';
import { InternalCacheService } from '@app/internal-cache';
import { ErrorHandlerService } from '../../utils/error/error-handler.service';
import { PortfolioOverviewDto } from './dtos/portfolio-overview.dto';
import { PortfolioPerformanceDto } from './dtos/portfolio-performance.dto';

@Injectable()
export class PortfolioService {
  private readonly logger = new Logger(PortfolioService.name);

  constructor(
    private readonly walletRepository: WalletRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly userRepository: UserRepository,
    private readonly portfolioRepository: PortfolioRepository,
    private readonly cacheService: InternalCacheService,
    private readonly errorHandler: ErrorHandlerService,
  ) {}

  async getPortfolioOverview(auth: AuthData): Promise<PortfolioOverviewDto> {
    const cacheKey = `portfolio:overview:${auth.id.toString()}`;

    try {
      const cachedData =
        await this.cacheService.get<PortfolioOverviewDto>(cacheKey);
      if (cachedData) {
        this.logger.log(`Portfolio overview cache hit for user ${auth.id}`);
        return cachedData;
      }

      const user = await this.userRepository.getUserByUserId(
        auth.id.toString(),
      );
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const wallets = await this.walletRepository.getUserWallets(user.id);
      const totalValue = await this.calculatePortfolioValue(wallets);

      const recentTransactions = await this.getRecentTransactions(user.id, 5);

      const overview: PortfolioOverviewDto = {
        totalValue: totalValue.toString(),
        assetCount: wallets.length,
        assetAllocation: this.calculateAssetAllocation(wallets, totalValue),
        wallets: wallets.map((wallet) => ({
          currency: wallet.currency.currencyCode,
          balance: wallet.balance,
          convertedBalance: wallet.convertedBalance,
        })),
        recentTransactions,
      };

      await this.cacheService
        .tags('portfolio', 'overview', `user:${auth.id}`)
        .set(cacheKey, overview, 300);

      this.logger.log(`Portfolio overview generated for user ${auth.id}`);
      return overview;
    } catch (error) {
      await this.errorHandler.handleError(
        error,
        'PortfolioService.getPortfolioOverview',
        {
          userId: auth.id,
        },
      );
      throw error;
    }
  }

  private calculateAssetAllocation(wallets: Wallet[], totalValue: number) {
    return wallets.map((wallet) => ({
      currency: wallet.currency.currencyCode,
      percentage: (parseFloat(wallet.convertedBalance) / totalValue) * 100,
      value: wallet.convertedBalance,
      balance: wallet.balance,
    }));
  }

  private async getRecentTransactions(userId: string, limit: number = 5) {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 30); // Last 30 days

      const { transactions } =
        await this.transactionRepository.getTransactionsByUser(
          userId,
          startDate,
          endDate,
          1,
          limit,
        );

      return transactions.map((transaction) => ({
        id: transaction.id,
        type: transaction.type,
        currency: transaction.currency,
        amount: transaction.amount,
        timestamp: transaction.createdAt,
      }));
    } catch (error) {
      this.logger.warn(
        `Failed to fetch recent transactions for user ${userId}:`,
        error,
      );
      return [];
    }
  }

  async getPortfolioPerformance(
    auth: AuthData,
  ): Promise<PortfolioPerformanceDto> {
    const cacheKey = `portfolio:performance:${auth.id.toString()}`;

    try {
      const cachedData =
        await this.cacheService.get<PortfolioPerformanceDto>(cacheKey);
      if (cachedData) {
        this.logger.log(`Portfolio performance cache hit for user ${auth.id}`);
        return cachedData;
      }

      const user = await this.userRepository.getUserByUserId(
        auth.id.toString(),
      );
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const wallets = await this.walletRepository.getUserWallets(user.id);
      const currentValue = await this.calculatePortfolioValue(wallets);

      const dayMetrics = await this.portfolioRepository.getPerformanceMetrics(
        user.id,
        'day',
      );
      const weekMetrics = await this.portfolioRepository.getPerformanceMetrics(
        user.id,
        'week',
      );
      const monthMetrics = await this.portfolioRepository.getPerformanceMetrics(
        user.id,
        'month',
      );

      const historicalData = await this.portfolioRepository.getHistoricalData(
        user.id,
        30,
      );

      const { totalProfitLoss, totalProfitLossPercent } =
        await this.calculateTotalProfitLoss(user.id, currentValue);

      const { bestPerformer, worstPerformer } =
        await this.calculateAssetPerformance(wallets, historicalData);

      const performance: PortfolioPerformanceDto = {
        currentValue: currentValue.toString(),
        dayChange: dayMetrics?.valueChange || '0',
        dayChangePercent: dayMetrics?.percentChange || '0',
        weekChange: weekMetrics?.valueChange || '0',
        weekChangePercent: weekMetrics?.percentChange || '0',
        monthChange: monthMetrics?.valueChange || '0',
        monthChangePercent: monthMetrics?.percentChange || '0',
        totalProfitLoss,
        totalProfitLossPercent,
        bestPerformer,
        worstPerformer,
        historicalData: historicalData.map((snapshot) => ({
          date: snapshot.snapshotDate,
          value: snapshot.totalValue,
          change: snapshot.dayChange || '0',
          changePercent: snapshot.dayChangePercent || '0',
        })),
        performanceMetrics: {
          day: dayMetrics,
          week: weekMetrics,
          month: monthMetrics,
        },
      };

      await this.cacheService
        .tags('portfolio', 'performance', `user:${auth.id}`)
        .set(cacheKey, performance, 600);

      this.logger.log(`Portfolio performance generated for user ${auth.id}`);
      return performance;
    } catch (error) {
      await this.errorHandler.handleError(
        error,
        'PortfolioService.getPortfolioPerformance',
        {
          userId: auth.id,
        },
      );
      throw error;
    }
  }

  private async calculateTotalProfitLoss(userId: string, currentValue: number) {
    try {
      const earliestSnapshot = await this.portfolioRepository.findOne({
        where: {
          user: { userId },
        },
        order: {
          snapshotDate: 'ASC',
        },
        relations: ['user'],
      });

      if (!earliestSnapshot) {
        return this.calculateProfitLossFromTransactions(userId, currentValue);
      }

      const initialValue = parseFloat(earliestSnapshot.totalValue);
      const profitLoss = currentValue - initialValue;
      const profitLossPercent =
        initialValue > 0 ? (profitLoss / initialValue) * 100 : 0;

      return {
        totalProfitLoss: profitLoss.toFixed(2),
        totalProfitLossPercent: profitLossPercent.toFixed(2),
      };
    } catch (error) {
      this.logger.warn(
        `Failed to calculate total profit/loss for user ${userId}:`,
        error,
      );
      return {
        totalProfitLoss: '0',
        totalProfitLossPercent: '0',
      };
    }
  }

  private async calculateProfitLossFromTransactions(
    userId: string,
    currentValue: number,
  ) {
    try {
      const allTransactions = await this.transactionRepository.find({
        where: {
          user: { userId },
          type: TransactionType.FUND,
          status: TransactionStatus.SUCCESS,
        },
        relations: ['user'],
      });

      const totalInvested = allTransactions.reduce((sum, transaction) => {
        return sum + parseFloat(transaction.total || transaction.amount);
      }, 0);

      if (totalInvested === 0) {
        return {
          totalProfitLoss: '0',
          totalProfitLossPercent: '0',
        };
      }

      const profitLoss = currentValue - totalInvested;
      const profitLossPercent = (profitLoss / totalInvested) * 100;

      return {
        totalProfitLoss: profitLoss.toFixed(2),
        totalProfitLossPercent: profitLossPercent.toFixed(2),
      };
    } catch (error) {
      this.logger.warn(
        `Failed to calculate profit/loss from transactions for user ${userId}:`,
        error,
      );
      return {
        totalProfitLoss: '0',
        totalProfitLossPercent: '0',
      };
    }
  }

  private async calculateAssetPerformance(
    wallets: Wallet[],
    historicalData: any[],
  ) {
    let bestPerformer = null;
    let worstPerformer = null;

    if (wallets.length === 0 || historicalData.length === 0) {
      return { bestPerformer, worstPerformer };
    }

    try {
      const oldestSnapshot = historicalData[0];
      const assetPerformances: Array<{
        currency: string;
        change: number;
        changePercent: number;
      }> = [];

      for (const wallet of wallets) {
        const currentValue = parseFloat(wallet.convertedBalance);
        if (currentValue <= 0) continue;

        const currency = wallet.currency.currencyCode;

        const historicalAsset = oldestSnapshot.assets?.find(
          (asset: any) => asset.currency === currency,
        );

        if (historicalAsset) {
          const historicalValue = parseFloat(historicalAsset.value || '0');
          const currentBalance = parseFloat(wallet.balance);
          const historicalBalance = parseFloat(historicalAsset.balance || '0');

          const currentPricePerUnit =
            currentBalance > 0 ? currentValue / currentBalance : 0;
          const historicalPricePerUnit =
            historicalBalance > 0 ? historicalValue / historicalBalance : 0;

          if (historicalPricePerUnit > 0) {
            const priceChange = currentPricePerUnit - historicalPricePerUnit;
            const priceChangePercent =
              (priceChange / historicalPricePerUnit) * 100;

            assetPerformances.push({
              currency,
              change: priceChange,
              changePercent: priceChangePercent,
            });
          }
        } else {
          const dayChangePercent =
            historicalData.length > 1
              ? this.calculateAssetDayChange(currency, historicalData)
              : 0;

          assetPerformances.push({
            currency,
            change: 0,
            changePercent: dayChangePercent,
          });
        }
      }

      if (assetPerformances.length > 0) {
        const sortedByPerformance = assetPerformances.sort(
          (a, b) => b.changePercent - a.changePercent,
        );

        bestPerformer = {
          currency: sortedByPerformance[0].currency,
          change: sortedByPerformance[0].change.toFixed(2),
          changePercent: sortedByPerformance[0].changePercent.toFixed(2),
        };

        worstPerformer = {
          currency:
            sortedByPerformance[sortedByPerformance.length - 1].currency,
          change:
            sortedByPerformance[sortedByPerformance.length - 1].change.toFixed(
              2,
            ),
          changePercent:
            sortedByPerformance[
              sortedByPerformance.length - 1
            ].changePercent.toFixed(2),
        };
      }
    } catch (error) {
      this.logger.warn('Failed to calculate asset performance:', error);
      const sortedByValue = wallets
        .filter((wallet) => parseFloat(wallet.convertedBalance) > 0)
        .sort(
          (a, b) =>
            parseFloat(b.convertedBalance) - parseFloat(a.convertedBalance),
        );

      if (sortedByValue.length > 0) {
        bestPerformer = {
          currency: sortedByValue[0].currency.currencyCode,
          change: '0',
          changePercent: '0',
        };

        worstPerformer = {
          currency:
            sortedByValue[sortedByValue.length - 1].currency.currencyCode,
          change: '0',
          changePercent: '0',
        };
      }
    }

    return { bestPerformer, worstPerformer };
  }

  private calculateAssetDayChange(
    currency: string,
    historicalData: any[],
  ): number {
    try {
      const recentSnapshots = historicalData.slice(-2);
      if (recentSnapshots.length < 2) return 0;

      const [older, newer] = recentSnapshots;
      const olderAsset = older.assets?.find(
        (asset: any) => asset.currency === currency,
      );
      const newerAsset = newer.assets?.find(
        (asset: any) => asset.currency === currency,
      );

      if (olderAsset && newerAsset) {
        const olderValue = parseFloat(olderAsset.value || '0');
        const newerValue = parseFloat(newerAsset.value || '0');
        const olderBalance = parseFloat(olderAsset.balance || '0');
        const newerBalance = parseFloat(newerAsset.balance || '0');

        if (olderBalance > 0 && newerBalance > 0) {
          const olderPrice = olderValue / olderBalance;
          const newerPrice = newerValue / newerBalance;

          if (olderPrice > 0) {
            return ((newerPrice - olderPrice) / olderPrice) * 100;
          }
        }
      }

      return 0;
    } catch {
      return 0;
    }
  }

  private async calculatePortfolioValue(wallets: Wallet[]): Promise<number> {
    return wallets.reduce(
      (total, wallet) => total + parseFloat(wallet.convertedBalance),
      0,
    );
  }
}
