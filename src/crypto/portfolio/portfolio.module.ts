import { Module } from '@nestjs/common';
import { PortfolioService } from './portfolio.service';
import { PortfolioController } from './portfolio.controller';
import { PortfolioSnapshotService } from './portfolio-snapshot.service';
import { PortfolioSnapshotCron } from './portfolio-snapshot.cron';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { QuidaxModule } from '@app/quidax';
import { UserRepository } from '../repositories/users.repository';
import { PortfolioRepository } from '../repositories/portfolio.repository';
import { CurrencyRepository } from '../repositories/currency.repository';
import { InternalCacheModule } from '@app/internal-cache';
import { ErrorHandlerService } from '../../utils/error/error-handler.service';
import { EventService } from '../../utils/events/event.service';
import { InternalCacheService } from '@app/internal-cache';
import {
  OrderCompletedPortfolioHandler,
  DepositSuccessPortfolioHandler,
  WithdrawalSuccessPortfolioHandler,
  SwapCompletedPortfolioHandler,
  DcaExecutionPortfolioHandler,
} from './portfolio.event-handlers';

@Module({
  imports: [QuidaxModule, InternalCacheModule],
  controllers: [PortfolioController],
  providers: [
    PortfolioService,
    PortfolioSnapshotService,
    PortfolioSnapshotCron,
    PortfolioRepository,
    WalletRepository,
    TransactionRepository,
    UserRepository,
    CurrencyRepository,
    ErrorHandlerService,
    InternalCacheService,
    EventService,
    OrderCompletedPortfolioHandler,
    DepositSuccessPortfolioHandler,
    WithdrawalSuccessPortfolioHandler,
    SwapCompletedPortfolioHandler,
    DcaExecutionPortfolioHandler,
  ],
  exports: [
    PortfolioService,
    PortfolioSnapshotService,
    // Export event handlers so other modules can use them
    OrderCompletedPortfolioHandler,
    DepositSuccessPortfolioHandler,
    WithdrawalSuccessPortfolioHandler,
    SwapCompletedPortfolioHandler,
    DcaExecutionPortfolioHandler,
  ],
})
export class PortfolioModule {}
