import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KycController } from './kyc.controller';
import { KycService } from './kyc.service';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxModule } from '@app/quidax';
import { User } from '../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    QuidaxModule,
  ],
  controllers: [KycController],
  providers: [
    KycService,
    UserRepository,
  ],
  exports: [KycService],
})
export class KycModule {}
