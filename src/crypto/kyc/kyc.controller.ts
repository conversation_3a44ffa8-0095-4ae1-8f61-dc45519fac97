import { Controller, Post, Get, Body, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';
import { KycService, KycDocumentDto } from './kyc.service';

@Controller('kyc')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('KYC')
export class KycController {
  constructor(private readonly kycService: KycService) {}

  @Post('submit')
  @ApiOperation({ summary: 'Submit KYC documents for verification' })
  @ApiResponse({ status: 200, description: 'KYC documents submitted successfully' })
  @ApiResponse({ status: 400, description: 'Invalid documents or user already verified' })
  async submitKycDocuments(
    @GetAuthData() auth: AuthData,
    @Body() kycData: KycDocumentDto,
  ) {
    return this.kycService.submitKycDocuments(auth, kycData);
  }

  @Get('status')
  @ApiOperation({ summary: 'Get KYC verification status' })
  @ApiResponse({ status: 200, description: 'KYC status retrieved successfully' })
  async getKycStatus(@GetAuthData() auth: AuthData) {
    return this.kycService.getKycStatus(auth);
  }

  @Get('requirements')
  @ApiOperation({ summary: 'Get KYC requirements and guidelines' })
  @ApiResponse({ status: 200, description: 'KYC requirements retrieved successfully' })
  async getKycRequirements() {
    return this.kycService.getKycRequirements();
  }
}
