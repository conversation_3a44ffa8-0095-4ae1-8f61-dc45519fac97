import { Controller, Get, Query, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { NewsService } from './news.service';

@Controller('news')
@ApiTags('Crypto News')
export class NewsController {
  constructor(private readonly newsService: NewsService) {}

  @Get()
  @ApiOperation({ summary: 'Get latest crypto news' })
  @ApiQuery({
    name: 'category',
    required: false,
    description: 'News category filter',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of articles to return',
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    description: 'Pagination offset',
  })
  @ApiResponse({
    status: 200,
    description: 'News articles retrieved successfully',
  })
  async getLatestNews(
    @Query('category') category?: string,
    @Query('limit') limit: string = '20',
    @Query('offset') offset: string = '0',
  ) {
    return this.newsService.getLatestNews(
      category,
      parseInt(limit),
      parseInt(offset),
    );
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get available news categories' })
  @ApiResponse({
    status: 200,
    description: 'News categories retrieved successfully',
  })
  async getNewsCategories() {
    return this.newsService.getNewsCategories();
  }

  @Get('sentiment')
  @ApiOperation({ summary: 'Get market sentiment analysis' })
  @ApiResponse({
    status: 200,
    description: 'Market sentiment retrieved successfully',
  })
  async getMarketSentiment() {
    return this.newsService.getMarketSentiment();
  }

  @Get('trending')
  @ApiOperation({ summary: 'Get trending topics' })
  @ApiResponse({
    status: 200,
    description: 'Trending topics retrieved successfully',
  })
  async getTrendingTopics() {
    return this.newsService.getTrendingTopics();
  }

  @Get('search')
  @ApiOperation({ summary: 'Search news articles' })
  @ApiQuery({ name: 'q', required: true, description: 'Search query' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of results to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
  })
  async searchNews(
    @Query('q') query: string,
    @Query('limit') limit: string = '10',
  ) {
    return this.newsService.searchNews(query, parseInt(limit));
  }

  @Get('coin/:symbol')
  @ApiOperation({ summary: 'Get news for specific cryptocurrency' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of articles to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Coin-specific news retrieved successfully',
  })
  async getNewsForCoin(
    @Param('symbol') coinSymbol: string,
    @Query('limit') limit: string = '10',
  ) {
    return this.newsService.getNewsForCoin(coinSymbol, parseInt(limit));
  }

  @Get('category/:category')
  @ApiOperation({ summary: 'Get news by category' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of articles to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Category news retrieved successfully',
  })
  async getNewsByCategory(
    @Param('category') category: string,
    @Query('limit') limit: string = '10',
  ) {
    return this.newsService.getNewsByCategory(category, parseInt(limit));
  }
}
