import { Injectable } from '@nestjs/common';
import { DataSource, <PERSON><PERSON>han, IsNull } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Notification, NotificationType, NotificationPriority } from '../entities/notification.entity';

@Injectable()
export class NotificationRepository extends TypeOrmRepository<Notification> {
  constructor(private readonly dataSource: DataSource) {
    super(Notification, dataSource.createEntityManager());
  }

  async findByUser(userId: string, limit: number = 20): Promise<Notification[]> {
    return this.find({
      where: {
        user: { userId },
        isActive: true,
      },
      order: {
        createdAt: 'DESC',
      },
      take: limit,
      relations: ['user'],
    });
  }

  async findUnreadByUser(userId: string): Promise<Notification[]> {
    return this.find({
      where: {
        user: { userId },
        isRead: false,
        isActive: true,
      },
      order: {
        createdAt: 'DESC',
      },
      relations: ['user'],
    });
  }

  async markAsRead(id: string): Promise<void> {
    await this.update(id, {
      isRead: true,
      readAt: new Date(),
    });
  }

  async markAllAsReadForUser(userId: string): Promise<void> {
    await this.update(
      {
        user: { userId },
        isRead: false,
      },
      {
        isRead: true,
        readAt: new Date(),
      },
    );
  }

  async findByTypeAndUser(
    userId: string,
    type: NotificationType,
    limit: number = 10,
  ): Promise<Notification[]> {
    return this.find({
      where: {
        user: { userId },
        type,
        isActive: true,
      },
      order: {
        createdAt: 'DESC',
      },
      take: limit,
      relations: ['user'],
    });
  }

  async findCriticalNotifications(): Promise<Notification[]> {
    return this.find({
      where: {
        priority: NotificationPriority.CRITICAL,
        isActive: true,
        expiresAt: MoreThan(new Date()),
      },
      order: {
        createdAt: 'DESC',
      },
      relations: ['user'],
    });
  }

  async cleanupExpiredNotifications(): Promise<void> {
    await this.update(
      {
        expiresAt: MoreThan(new Date()),
      },
      {
        isActive: false,
      },
    );
  }
}
