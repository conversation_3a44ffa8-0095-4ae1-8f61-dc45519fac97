import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Nft } from '../entities/nft.entity';

@Injectable()
export class NftRepository extends TypeOrmRepository<Nft> {
  constructor(private readonly dataSource: DataSource) {
    super(Nft, dataSource.createEntityManager());
  }

  async findByUser(userId: string): Promise<Nft[]> {
    return this.find({
      where: {
        user: { userId },
        isOwned: true,
      },
      relations: ['user'],
      order: {
        acquiredAt: 'DESC',
      },
    });
  }

  async findByUserAndCollection(userId: string, collectionSlug: string): Promise<Nft[]> {
    return this.find({
      where: {
        user: { userId },
        collectionSlug,
        isOwned: true,
      },
      relations: ['user'],
      order: {
        tokenId: 'ASC',
      },
    });
  }

  async findByContractAndToken(contractAddress: string, tokenId: string): Promise<Nft | null> {
    return this.findOne({
      where: {
        contractAddress,
        tokenId,
      },
      relations: ['user'],
    });
  }

  async getUserCollections(userId: string): Promise<any[]> {
    const result = await this.createQueryBuilder('nft')
      .select([
        'nft.collectionName as collectionName',
        'nft.collectionSlug as collectionSlug',
        'nft.blockchain as blockchain',
        'COUNT(nft.id) as nftCount',
        'AVG(nft.floorPrice) as avgFloorPrice',
        'SUM(nft.floorPrice) as totalFloorValue',
        'nft.floorPriceCurrency as currency',
      ])
      .where('nft.user.userId = :userId', { userId })
      .andWhere('nft.isOwned = :isOwned', { isOwned: true })
      .groupBy('nft.collectionName, nft.collectionSlug, nft.blockchain, nft.floorPriceCurrency')
      .getRawMany();

    return result;
  }

  async getPortfolioStats(userId: string): Promise<any> {
    const stats = await this.createQueryBuilder('nft')
      .select([
        'COUNT(nft.id) as totalNfts',
        'COUNT(DISTINCT nft.collectionName) as totalCollections',
        'SUM(nft.floorPrice) as totalFloorValue',
        'SUM(nft.acquiredPrice) as totalAcquiredValue',
        'AVG(nft.floorPrice) as avgFloorPrice',
      ])
      .where('nft.user.userId = :userId', { userId })
      .andWhere('nft.isOwned = :isOwned', { isOwned: true })
      .getRawOne();

    return stats;
  }

  async getTopNftsByValue(userId: string, limit: number = 10): Promise<Nft[]> {
    return this.find({
      where: {
        user: { userId },
        isOwned: true,
      },
      relations: ['user'],
      order: {
        floorPrice: 'DESC',
      },
      take: limit,
    });
  }

  async getRecentlyAcquired(userId: string, limit: number = 10): Promise<Nft[]> {
    return this.find({
      where: {
        user: { userId },
        isOwned: true,
      },
      relations: ['user'],
      order: {
        acquiredAt: 'DESC',
      },
      take: limit,
    });
  }

  async updateNftPrices(contractAddress: string, tokenId: string, priceData: any): Promise<void> {
    await this.update(
      {
        contractAddress,
        tokenId,
      },
      {
        floorPrice: priceData.floorPrice,
        floorPriceCurrency: priceData.currency,
        lastUpdated: new Date(),
      },
    );
  }

  async markAsTransferred(contractAddress: string, tokenId: string, newOwnerUserId?: string): Promise<void> {
    const updateData: any = {
      isOwned: false,
      lastUpdated: new Date(),
    };

    if (newOwnerUserId) {
      updateData.user = { userId: newOwnerUserId };
      updateData.isOwned = true;
      updateData.acquiredAt = new Date();
    }

    await this.update(
      {
        contractAddress,
        tokenId,
      },
      updateData,
    );
  }
}
