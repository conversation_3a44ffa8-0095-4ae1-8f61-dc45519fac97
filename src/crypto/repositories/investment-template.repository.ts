import { Injectable } from '@nestjs/common';
import { DataSource, Between } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { InvestmentTemplate, RiskLevel } from '../entities/investment-template.entity';

@Injectable()
export class InvestmentTemplateRepository extends TypeOrmRepository<InvestmentTemplate> {
  constructor(private readonly dataSource: DataSource) {
    super(InvestmentTemplate, dataSource.createEntityManager());
  }

  async findByRiskProfile(riskScore: number): Promise<InvestmentTemplate[]> {
    return this.find({
      where: {
        minRiskScore: Between(0, riskScore),
        maxRiskScore: Between(riskScore, 100),
        isActive: true,
      },
      order: {
        popularity: 'DESC',
      },
    });
  }

  async findByRiskLevel(riskLevel: RiskLevel): Promise<InvestmentTemplate[]> {
    return this.find({
      where: {
        riskLevel,
        isActive: true,
      },
      order: {
        popularity: 'DESC',
      },
    });
  }

  async findPopular(limit: number = 5): Promise<InvestmentTemplate[]> {
    return this.find({
      where: {
        isActive: true,
      },
      order: {
        popularity: 'DESC',
      },
      take: limit,
    });
  }
}
