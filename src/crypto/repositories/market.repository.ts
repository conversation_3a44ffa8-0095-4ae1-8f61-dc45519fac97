import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MarketDataDto } from '../dtos/market.dto';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Market } from '../entities/markets.entity';

@Injectable()
export class MarketRepository extends TypeOrmRepository<Market> {
  constructor(private readonly dataSource: DataSource) {
    super(Market, dataSource.createEntityManager());
  }

  async createOrUpdateMarkets(marketData: MarketDataDto[]) {
    marketData.forEach(async (market) => {
      const existingMarket = await this.findOne({
        where: { id: market.id },
      });
      if (existingMarket) {
        await this.update({ id: market.id }, market);
      } else {
        await this.save(market);
      }
    });
  }

  async getMarkets(): Promise<Market[]> {
    return await this.find();
  }

  async getMarketById(id: string): Promise<Market> {
    return await this.findOne({
      where: { id },
    });
  }

  async getMarketsByQuotes(): Promise<Record<string, string[]>> {
    const markets = await this.find({
      select: ['id', 'baseUnit', 'quoteUnit'],
    });

    // Group baseUnits by quoteUnit
    const groupedMarkets: Record<string, string[]> = {};

    markets.forEach(({ baseUnit, quoteUnit }) => {
      if (!groupedMarkets[quoteUnit]) {
        groupedMarkets[quoteUnit] = [];
      }
      if (!groupedMarkets[quoteUnit].includes(baseUnit)) {
        groupedMarkets[quoteUnit].push(baseUnit);
      }
    });

    return groupedMarkets;
  }
}
