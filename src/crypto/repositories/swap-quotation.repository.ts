import { Injectable, NotFoundException } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { SwapQuotation } from '../entities/swap-quotation.entity';
import { DataSource } from 'typeorm';
import { UserRepository } from './users.repository';

export interface CreateSwapQuotationDto {
  id: string;
  userId: string;
  from_currency: string;
  to_currency: string;
  quoted_price: string;
  quoted_currency: string;
  from_amount: string;
  to_amount: string;
  confirmed?: boolean;
  expires_at: string;
}

@Injectable()
export class SwapQuotationRepository extends TypeOrmRepository<SwapQuotation> {
  constructor(
    dataSource: DataSource,
    private readonly userRepository: UserRepository,
  ) {
    super(SwapQuotation, dataSource.createEntityManager());
  }

  async createSwapQuotation(
    quotation: CreateSwapQuotationDto,
  ): Promise<SwapQuotation> {
    const user = await this.userRepository.findOne({
      where: {
        userId: quotation.userId,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const savedQuotation = await this.save({
      ...quotation,
      user,
    });

    return savedQuotation;
  }

  async getSwapQuotation(quotationId: string): Promise<SwapQuotation> {
    const quotation = await this.findOne({
      where: {
        id: quotationId,
      },
      relations: ['user'],
    });

    if (!quotation) {
      throw new NotFoundException(
        `Swap quotation with ID ${quotationId} not found`,
      );
    }

    return quotation;
  }

  async getSwapQuotationsByUserId(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ quotations: SwapQuotation[]; total: number }> {
    const query = this.createQueryBuilder('swap_quotations')
      .where('swap_quotations.userId = :userId', { userId })
      .andWhere('swap_quotations.createdAt >= :startDate', { startDate })
      .andWhere('swap_quotations.createdAt <= :endDate', { endDate })
      .orderBy('swap_quotations.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('swap_quotations.user', 'user');

    const [quotations, total] = await query.getManyAndCount();

    return { quotations, total };
  }

  async updateSwapQuotationConfirmation(
    quotationId: string,
    confirmed: boolean,
  ): Promise<SwapQuotation> {
    const quotation = await this.getSwapQuotation(quotationId);

    quotation.confirmed = confirmed;

    return await this.save(quotation);
  }
}
