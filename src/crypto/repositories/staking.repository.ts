import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { StakingPosition, StakingStatus } from '../entities/staking-position.entity';

@Injectable()
export class StakingRepository extends TypeOrmRepository<StakingPosition> {
  constructor(private readonly dataSource: DataSource) {
    super(StakingPosition, dataSource.createEntityManager());
  }

  async findByUser(userId: string): Promise<StakingPosition[]> {
    return this.find({
      where: {
        user: { userId },
        isActive: true,
      },
      relations: ['user'],
      order: {
        startDate: 'DESC',
      },
    });
  }

  async findActivePositions(userId: string): Promise<StakingPosition[]> {
    return this.find({
      where: {
        user: { userId },
        status: StakingStatus.ACTIVE,
        isActive: true,
      },
      relations: ['user'],
      order: {
        startDate: 'DESC',
      },
    });
  }

  async findByUserAndCurrency(userId: string, currency: string): Promise<StakingPosition[]> {
    return this.find({
      where: {
        user: { userId },
        currency,
        isActive: true,
      },
      relations: ['user'],
      order: {
        startDate: 'DESC',
      },
    });
  }

  async getStakingStats(userId: string): Promise<any> {
    const stats = await this.createQueryBuilder('staking')
      .select([
        'COUNT(staking.id) as totalPositions',
        'SUM(staking.amount) as totalStaked',
        'SUM(staking.totalRewardsEarned) as totalRewards',
        'AVG(staking.apy) as avgApy',
      ])
      .where('staking.user.userId = :userId', { userId })
      .andWhere('staking.isActive = :isActive', { isActive: true })
      .getRawOne();

    return stats;
  }

  async getStakingByProtocol(userId: string): Promise<any[]> {
    const result = await this.createQueryBuilder('staking')
      .select([
        'staking.protocol as protocol',
        'staking.currency as currency',
        'COUNT(staking.id) as positionCount',
        'SUM(staking.amount) as totalAmount',
        'SUM(staking.totalRewardsEarned) as totalRewards',
        'AVG(staking.apy) as avgApy',
      ])
      .where('staking.user.userId = :userId', { userId })
      .andWhere('staking.isActive = :isActive', { isActive: true })
      .groupBy('staking.protocol, staking.currency')
      .getRawMany();

    return result;
  }

  async updateRewards(id: string, rewardAmount: string): Promise<void> {
    const position = await this.findOne({ where: { id } });
    if (position) {
      const newTotalRewards = (parseFloat(position.totalRewardsEarned) + parseFloat(rewardAmount)).toString();
      const newCurrentRewards = (parseFloat(position.rewards) + parseFloat(rewardAmount)).toString();

      await this.update(id, {
        rewards: newCurrentRewards,
        totalRewardsEarned: newTotalRewards,
        lastRewardDate: new Date(),
      });
    }
  }

  async requestUnstake(id: string): Promise<void> {
    await this.update(id, {
      status: StakingStatus.UNSTAKING,
      unstakeRequestDate: new Date(),
    });
  }

  async completeUnstake(id: string): Promise<void> {
    await this.update(id, {
      status: StakingStatus.COMPLETED,
      unstakeCompletionDate: new Date(),
      endDate: new Date(),
    });
  }

  async findPositionsDueForRewards(): Promise<StakingPosition[]> {
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    return this.find({
      where: {
        status: StakingStatus.ACTIVE,
        isActive: true,
      },
      relations: ['user'],
    });
  }

  async findExpiredLockPositions(): Promise<StakingPosition[]> {
    const now = new Date();

    return this.createQueryBuilder('staking')
      .where('staking.status = :status', { status: StakingStatus.ACTIVE })
      .andWhere('staking.isActive = :isActive', { isActive: true })
      .andWhere('staking.lockPeriodDays IS NOT NULL')
      .andWhere(
        'DATE_ADD(staking.startDate, INTERVAL staking.lockPeriodDays DAY) <= :now',
        { now },
      )
      .getMany();
  }
}
