import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ComplianceService } from './compliance.service';
import { TransactionRepository } from '../repositories/transaction.repository';
import { UserRepository } from '../repositories/users.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { CurrencyRepository } from '../repositories/currency.repository';
import { EventService } from '../../utils/events/event.service';
import { Transaction } from '../entities/transactions.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Transaction, User])],
  providers: [
    ComplianceService,
    TransactionRepository,
    UserRepository,
    WalletRepository,
    CurrencyRepository,
    EventService,
  ],
  exports: [ComplianceService],
})
export class ComplianceModule {}
