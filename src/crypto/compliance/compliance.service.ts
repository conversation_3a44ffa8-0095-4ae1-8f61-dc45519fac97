import { Injectable, Logger } from '@nestjs/common';
import { TransactionRepository } from '../repositories/transaction.repository';
import { UserRepository } from '../repositories/users.repository';
import { EventService } from '../../utils/events/event.service';
import { Transaction } from '../entities/transactions.entity';
import { User } from '../entities/user.entity';

@Injectable()
export class ComplianceService {
  private readonly logger = new Logger(ComplianceService.name);

  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly userRepository: UserRepository,
    private readonly eventService: EventService,
  ) {}

  async monitorTransaction(transactionId: string) {
    const transaction = await this.transactionRepository.findOne({
      where: { id: transactionId },
      relations: ['user'],
    });

    if (!transaction) {
      throw new Error('Transaction not found');
    }

    const user = transaction.user;

    // Check transaction against risk rules
    const riskScore = await this.calculateTransactionRiskScore(
      transaction,
      user,
    );

    // Log for audit purposes
    this.logger.log(`Transaction ${transactionId} risk score: ${riskScore}`);

    // Create audit trail
    await this.createAuditTrail({
      userId: user.id,
      action: 'TRANSACTION_RISK_ASSESSMENT',
      details: {
        transactionId,
        riskScore,
        timestamp: new Date(),
      },
    });

    // If high risk, flag for review
    if (riskScore > 80) {
      await this.flagTransactionForReview(transaction, riskScore);

      // Emit event for notification
      await this.eventService.emitEvent('compliance.high_risk_transaction', {
        transactionId,
        userId: user.id,
        riskScore,
      });
    }

    return { riskScore };
  }

  async createAuditTrail(auditData: any) {
    // Implementation to store audit trail
    // This could be in a separate audit table or sent to a logging service
    this.logger.debug(`Audit trail created: ${JSON.stringify(auditData)}`);
  }

  private async calculateTransactionRiskScore(
    transaction: Transaction,
    user: User,
  ) {
    // Implementation of risk scoring algorithm
    // Factors could include:
    // - Transaction amount
    // - User history
    // - Time of day
    // - Destination address
    // - Geographic location

    let score = 0;

    // Example scoring factors
    if (parseFloat(transaction.amount) > 10000) {
      score += 30;
    }

    if (user.transactionCount < 5) {
      score += 20;
    }

    // More scoring logic...

    return score;
  }

  private async flagTransactionForReview(
    transaction: Transaction,
    riskScore: number,
  ) {
    // Update transaction status or create a review task
    // Note: Since requiresReview field doesn't exist in Transaction entity,
    // we'll use the status field or create an audit trail instead
    await this.createAuditTrail({
      transactionId: transaction.id,
      action: 'flagged_for_review',
      riskScore,
      timestamp: new Date(),
      reason: 'High risk score detected',
    });
  }
}
