import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { Orders } from './orders.entity';
import { User } from './user.entity';

@Entity('trades')
export class Trade extends BaseEntity {
  @ManyToOne(() => Orders, (order) => order.trades)
  order: Orders;

  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @Column()
  market_id: string;

  @Column()
  market_base_unit: string;

  @Column()
  market_quote_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  price_amount: string;

  @Column()
  price_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  volume_amount: string;

  @Column()
  volume_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  total_amount: string;

  @Column()
  total_unit: string;
}
