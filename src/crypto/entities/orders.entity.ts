import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';
import { Trade } from './trades.entity';

export enum OrderSide {
  BUY = 'buy',
  SELL = 'sell',
}

export enum OrderType {
  LIMIT = 'limit',
  MARKET = 'market',
}

export enum OrderStatus {
  PENDING = 'pending',
  DONE = 'done',
  CANCEL = 'cancel',
  WAIT = 'wait',
}

@Entity('orders')
export class Orders extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @OneToMany(() => Trade, (trade) => trade.order, { cascade: true })
  trades: Trade[];

  @Column({ unique: true })
  reference: string;

  @Column({ nullable: true })
  market_id: string;

  @Column({ nullable: true })
  base_unit: string;

  @Column({ nullable: true })
  quote_unit: string;

  @Column({ type: 'enum', enum: OrderSide })
  side: OrderSide;

  @Column({ type: 'enum', enum: OrderType })
  order_type: OrderType;

  @Column()
  price_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  price_amount: string;

  @Column()
  avg_price_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  avg_price_amount: string;

  @Column({ nullable: true })
  volume_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  volume_amount: string;

  @Column({ nullable: true })
  origin_volume_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  origin_volume_amount: string;

  @Column({ nullable: true })
  executed_volume_unit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  executed_volume_amount: string;

  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.PENDING })
  status: OrderStatus;

  @Column({ nullable: true })
  trades_count: string;

  @Column({ type: 'json', nullable: true })
  meta: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  errors: Record<string, any>;
}
