import { Entity, Column, ManyToOne, Index } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';

export enum NftStandard {
  ERC721 = 'ERC721',
  ERC1155 = 'ERC1155',
  BEP721 = 'BEP721',
  BEP1155 = 'BEP1155',
}

export interface NftMetadata {
  name: string;
  description: string;
  image: string;
  attributes?: Array<{
    trait_type: string;
    value: string | number;
  }>;
  external_url?: string;
  animation_url?: string;
}

@Entity('nfts')
@Index(['user', 'contractAddress', 'tokenId'])
export class Nft extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column()
  contractAddress: string;

  @Column()
  tokenId: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  imageUrl: string;

  @Column({ type: 'json', nullable: true })
  metadata: NftMetadata;

  @Column({
    type: 'enum',
    enum: NftStandard,
    default: NftStandard.ERC721,
  })
  standard: NftStandard;

  @Column()
  blockchain: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  floorPrice: string;

  @Column({ nullable: true })
  floorPriceCurrency: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  lastSalePrice: string;

  @Column({ nullable: true })
  lastSalePriceCurrency: string;

  @Column({ nullable: true })
  lastSaleDate: Date;

  @Column()
  collectionName: string;

  @Column({ nullable: true })
  collectionSlug: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 1 })
  quantity: string;

  @Column({ default: true })
  isOwned: boolean;

  @Column({ nullable: true })
  acquiredAt: Date;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  acquiredPrice: string;

  @Column({ nullable: true })
  acquiredPriceCurrency: string;

  @Column({ nullable: true })
  rarityRank: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  rarityScore: string;

  @Column({ default: false })
  isListed: boolean;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  listingPrice: string;

  @Column({ nullable: true })
  listingPriceCurrency: string;

  @Column({ nullable: true })
  marketplace: string;

  @Column({ nullable: true })
  lastUpdated: Date;
}
