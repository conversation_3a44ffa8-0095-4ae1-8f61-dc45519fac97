import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';

export enum RiskLevel {
  CONSERVATIVE = 'conservative',
  MODERATE = 'moderate',
  AGGRESSIVE = 'aggressive',
}

export interface AssetAllocation {
  currency: string;
  percentage: number;
  description?: string;
}

@Entity('investment_templates')
export class InvestmentTemplate extends BaseEntity {
  @Column()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: RiskLevel,
  })
  riskLevel: RiskLevel;

  @Column({ type: 'int' })
  minRiskScore: number;

  @Column({ type: 'int' })
  maxRiskScore: number;

  @Column({ type: 'json' })
  allocations: AssetAllocation[];

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  minimumInvestment: string;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  expectedAnnualReturn: string;

  @Column({ type: 'text', nullable: true })
  strategy: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 0 })
  popularity: number;
}
