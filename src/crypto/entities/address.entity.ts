import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';

export enum AddressStatus {
  PROCESSING = 'processing',
  SUCCESS = 'success',
}

@Entity('addresses')
export class Address extends BaseEntity {
  @Column()
  currency: string;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  destination_tag: string;

  @Column({ nullable: true })
  total_payments: string;

  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column()
  network: string;

  @Column({
    type: 'enum',
    enum: AddressStatus,
    default: AddressStatus.PROCESSING,
  })
  status: AddressStatus;
}
