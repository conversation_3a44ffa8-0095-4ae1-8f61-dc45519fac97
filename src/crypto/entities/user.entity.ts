import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';

export enum KycStatus {
  PENDING = 'pending',
  UNDER_REVIEW = 'under_review',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
}

export enum RiskProfile {
  CONSERVATIVE = 'conservative',
  MODERATE = 'moderate',
  AGGRESSIVE = 'aggressive',
}

@Entity('users')
export class User extends BaseEntity {
  @Column({ nullable: false })
  userId: string;

  @Column({ nullable: false })
  firstName: string;

  @Column({ nullable: false })
  lastName: string;

  @Column({ nullable: false })
  sn: string;

  @Column({ nullable: false })
  email: string;

  @Column({ nullable: true })
  reference: string;

  @Column({ nullable: true })
  displayName: string;

  // KYC Fields
  @Column({ default: false })
  kycVerified: boolean;

  @Column({
    type: 'enum',
    enum: KycStatus,
    default: KycStatus.PENDING,
  })
  kycStatus: KycStatus;

  @Column({ nullable: true })
  kycSubmittedAt: Date;

  @Column({ nullable: true })
  kycVerifiedAt: Date;

  @Column({ nullable: true })
  kycRejectionReason: string;

  // Security Fields
  @Column({ default: false })
  twoFactorEnabled: boolean;

  @Column({ nullable: true })
  twoFactorSecret: string;

  @Column({ nullable: true })
  tempTwoFactorSecret: string;

  @Column({ nullable: true })
  publicKey: string;

  @Column({ nullable: true })
  lastLoginAt: Date;

  @Column({ nullable: true })
  lastLoginIp: string;

  // Portfolio & Trading
  @Column({ default: false })
  hasWallet: boolean;

  @Column({ default: false })
  hasTransaction: boolean;

  @Column({
    type: 'enum',
    enum: RiskProfile,
    nullable: true,
  })
  riskProfile: RiskProfile;

  @Column({ type: 'int', nullable: true })
  riskScore: number;

  @Column({ type: 'int', default: 0 })
  transactionCount: number;

  @Column({ default: true })
  isActive: boolean;
}
