import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';

@Entity('whitelisted_addresses')
export class WhitelistedAddress extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column()
  address: string;

  @Column()
  label: string;

  @Column()
  currency: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  verificationCode: string;

  @Column({ default: false })
  isVerified: boolean;

  @Column({ nullable: true })
  verifiedAt: Date;
}
