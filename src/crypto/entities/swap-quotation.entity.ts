import { Column, Entity, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { BaseEntity } from '../../config/repository/base-entity';

@Entity('swap_quotations')
export class SwapQuotation extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @Column()
  from_currency: string;

  @Column()
  to_currency: string;

  @Column()
  quoted_price: string;

  @Column()
  quoted_currency: string;

  @Column()
  from_amount: string;

  @Column()
  to_amount: string;

  @Column({ default: false })
  confirmed: boolean;

  @Column()
  expires_at: string;
}
