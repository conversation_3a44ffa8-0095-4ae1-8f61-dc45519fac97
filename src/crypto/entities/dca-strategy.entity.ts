import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';

export enum DcaFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export enum DcaStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('dca_strategies')
export class DcaStrategy extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column()
  currency: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  amount: string;

  @Column({
    type: 'enum',
    enum: DcaFrequency,
  })
  frequency: DcaFrequency;

  @Column({
    type: 'enum',
    enum: DcaStatus,
    default: DcaStatus.ACTIVE,
  })
  status: DcaStatus;

  @Column({ nullable: true })
  lastExecutionDate: Date;

  @Column({ nullable: true })
  nextExecutionDate: Date;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  totalInvested: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  totalTokensAcquired: string;

  @Column({ type: 'int', default: 0 })
  executionCount: number;

  @Column({ nullable: true })
  endDate: Date;

  @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true })
  maxInvestment: string;
}
