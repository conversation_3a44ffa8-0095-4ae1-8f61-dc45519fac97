import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';

export enum NotificationType {
  TRANSACTION_COMPLETED = 'transaction_completed',
  TRANSACTION_FAILED = 'transaction_failed',
  PRICE_ALERT = 'price_alert',
  SECURITY_ALERT = 'security_alert',
  SYSTEM_MAINTENANCE = 'system_maintenance',
  KYC_UPDATE = 'kyc_update',
  WALLET_UPDATE = 'wallet_update',
  ORDER_UPDATE = 'order_update',
  DCA_EXECUTION = 'dca_execution',
  COMPLIANCE_ALERT = 'compliance_alert',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

@Entity('notifications')
export class Notification extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column()
  title: string;

  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'json', nullable: true })
  data: any;

  @Column({ default: false })
  isRead: boolean;

  @Column({ nullable: true })
  readAt: Date;

  @Column({
    type: 'enum',
    enum: NotificationPriority,
    default: NotificationPriority.MEDIUM,
  })
  priority: NotificationPriority;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  expiresAt: Date;

  @Column({ nullable: true })
  actionUrl: string;

  @Column({ nullable: true })
  actionLabel: string;
}
