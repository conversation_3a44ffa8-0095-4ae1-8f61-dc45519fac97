import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';

export enum StakingStatus {
  ACTIVE = 'active',
  UNSTAKING = 'unstaking',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum StakingType {
  FLEXIBLE = 'flexible',
  LOCKED = 'locked',
  DEFI = 'defi',
}

@Entity('staking_positions')
export class StakingPosition extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
  user: User;

  @Column()
  currency: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  amount: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  rewards: string;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  apy: string;

  @Column({
    type: 'enum',
    enum: StakingType,
  })
  stakingType: StakingType;

  @Column({
    type: 'enum',
    enum: StakingStatus,
    default: StakingStatus.ACTIVE,
  })
  status: StakingStatus;

  @Column()
  startDate: Date;

  @Column({ nullable: true })
  endDate: Date;

  @Column({ nullable: true })
  lockPeriodDays: number;

  @Column({ nullable: true })
  unstakeRequestDate: Date;

  @Column({ nullable: true })
  unstakeCompletionDate: Date;

  @Column({ nullable: true })
  lastRewardDate: Date;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: 0 })
  totalRewardsEarned: string;

  @Column({ nullable: true })
  stakingPoolId: string;

  @Column({ nullable: true })
  stakingPoolName: string;

  @Column({ nullable: true })
  protocol: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ default: true })
  isActive: boolean;
}
