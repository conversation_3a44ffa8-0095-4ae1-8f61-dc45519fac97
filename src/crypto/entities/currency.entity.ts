import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON><PERSON>, JoinTable } from 'typeorm';
import { Network } from './network.entity';
import { BaseEntity } from '../../config/repository/base-entity';

@Entity('currencies')
export class Currency extends BaseEntity {
  @Column()
  name: string;

  @Column({ unique: true })
  currencyCode: string;

  @Column({ default: true })
  isCrypto: boolean;

  @Column({ nullable: true })
  referenceCurrency: string;

  @Column({ default: false })
  blockchainEnabled: boolean;

  @Column({ nullable: true })
  defaultNetwork: string;

  @ManyToMany(() => Network, (network) => network.currencies)
  @JoinTable({
    name: 'currency_network_mapping',
    joinColumn: { name: 'currency_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'network_id', referencedColumnName: 'id' },
  })
  networks: Network[];
}
