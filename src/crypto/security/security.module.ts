import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecurityService } from './security.service';
import { TwoFactorService } from './two-factor.service';
import { TwoFactorController } from './two-factor.controller';
import { UserRepository } from '../repositories/users.repository';
import { WhitelistedAddressRepository } from '../repositories/whitelisted-address.repository';
import { User } from '../entities/user.entity';
import { WhitelistedAddress } from '../entities/whitelisted-address.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, WhitelistedAddress])],
  controllers: [TwoFactorController],
  providers: [
    SecurityService,
    TwoFactorService,
    UserRepository,
    WhitelistedAddressRepository,
  ],
  exports: [SecurityService, TwoFactorService],
})
export class SecurityModule {}
