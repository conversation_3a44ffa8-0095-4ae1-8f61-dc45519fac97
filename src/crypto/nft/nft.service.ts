import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { NftRepository } from '../repositories/nft.repository';
import { UserRepository } from '../repositories/users.repository';
import { AuthData } from '@crednet/authmanager';
import { Nft, NftStandard } from '../entities/nft.entity';

export interface NftPortfolioDto {
  totalNfts: number;
  totalCollections: number;
  totalFloorValue: string;
  totalAcquiredValue: string;
  profitLoss: string;
  profitLossPercent: string;
  topCollections: any[];
  recentlyAcquired: Nft[];
  topValueNfts: Nft[];
}

@Injectable()
export class NftService {
  private readonly logger = new Logger(NftService.name);

  constructor(
    private readonly nftRepository: NftRepository,
    private readonly userRepository: UserRepository,
  ) {}

  async getUserNftPortfolio(auth: AuthData): Promise<NftPortfolioDto> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Get portfolio statistics
    const stats = await this.nftRepository.getPortfolioStats(user.userId);

    // Get collections data
    const collections = await this.nftRepository.getUserCollections(
      user.userId,
    );

    // Get recently acquired NFTs
    const recentlyAcquired = await this.nftRepository.getRecentlyAcquired(
      user.userId,
      5,
    );

    // Get top value NFTs
    const topValueNfts = await this.nftRepository.getTopNftsByValue(
      user.userId,
      5,
    );

    // Calculate profit/loss
    const totalFloorValue = parseFloat(stats.totalFloorValue || '0');
    const totalAcquiredValue = parseFloat(stats.totalAcquiredValue || '0');
    const profitLoss = totalFloorValue - totalAcquiredValue;
    const profitLossPercent =
      totalAcquiredValue > 0 ? (profitLoss / totalAcquiredValue) * 100 : 0;

    return {
      totalNfts: parseInt(stats.totalNfts || '0'),
      totalCollections: parseInt(stats.totalCollections || '0'),
      totalFloorValue: stats.totalFloorValue || '0',
      totalAcquiredValue: stats.totalAcquiredValue || '0',
      profitLoss: profitLoss.toString(),
      profitLossPercent: profitLossPercent.toFixed(2),
      topCollections: collections.slice(0, 10),
      recentlyAcquired,
      topValueNfts,
    };
  }

  async getUserNfts(auth: AuthData, collectionSlug?: string): Promise<Nft[]> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (collectionSlug) {
      return this.nftRepository.findByUserAndCollection(
        user.userId,
        collectionSlug,
      );
    }

    return this.nftRepository.findByUser(user.userId);
  }

  async getUserCollections(auth: AuthData): Promise<any[]> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return this.nftRepository.getUserCollections(user.userId);
  }

  async addNftToPortfolio(auth: AuthData, nftData: any): Promise<Nft> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Check if NFT already exists
    const existingNft = await this.nftRepository.findByContractAndToken(
      nftData.contractAddress,
      nftData.tokenId,
    );

    if (existingNft && existingNft.isOwned) {
      throw new BadRequestException('NFT already exists in portfolio');
    }

    const nft = await this.nftRepository.save({
      user,
      contractAddress: nftData.contractAddress,
      tokenId: nftData.tokenId,
      name: nftData.name,
      description: nftData.description,
      imageUrl: nftData.imageUrl,
      metadata: nftData.metadata,
      standard: nftData.standard || NftStandard.ERC721,
      blockchain: nftData.blockchain,
      collectionName: nftData.collectionName,
      collectionSlug: nftData.collectionSlug,
      quantity: nftData.quantity || '1',
      isOwned: true,
      acquiredAt: new Date(),
      acquiredPrice: nftData.acquiredPrice,
      acquiredPriceCurrency: nftData.acquiredPriceCurrency,
      floorPrice: nftData.floorPrice,
      floorPriceCurrency: nftData.floorPriceCurrency,
      rarityRank: nftData.rarityRank,
      rarityScore: nftData.rarityScore,
    });

    this.logger.log(
      `NFT added to portfolio for user ${user.id}: ${nftData.contractAddress}/${nftData.tokenId}`,
    );

    return nft;
  }

  async removeNftFromPortfolio(auth: AuthData, nftId: string): Promise<void> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const nft = await this.nftRepository.findOne({
      where: {
        id: nftId,
        user: { userId: user.userId },
      },
    });

    if (!nft) {
      throw new BadRequestException('NFT not found in your portfolio');
    }

    await this.nftRepository.update(nftId, {
      isOwned: false,
      lastUpdated: new Date(),
    });

    this.logger.log(
      `NFT removed from portfolio for user ${user.id}: ${nft.contractAddress}/${nft.tokenId}`,
    );
  }

  async updateNftPrices(): Promise<void> {
    // This would typically integrate with NFT marketplaces like OpenSea, LooksRare, etc.
    // For now, this is a placeholder for the price update logic
    this.logger.log('Updating NFT prices from external sources...');

    // Implementation would:
    // 1. Get all unique NFT contracts and tokens
    // 2. Fetch current floor prices from marketplaces
    // 3. Update the database with new prices
    // 4. Calculate portfolio value changes

    // Placeholder implementation
    const allNfts = await this.nftRepository.find({
      where: { isOwned: true },
    });

    for (const nft of allNfts) {
      // Simulate price update (in real implementation, fetch from marketplace APIs)
      const mockPriceData = {
        floorPrice: nft.floorPrice,
        currency: nft.floorPriceCurrency || 'ETH',
      };

      await this.nftRepository.updateNftPrices(
        nft.contractAddress,
        nft.tokenId,
        mockPriceData,
      );
    }

    this.logger.log('NFT price update completed');
  }

  async getNftDetails(auth: AuthData, nftId: string): Promise<Nft> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const nft = await this.nftRepository.findOne({
      where: {
        id: nftId,
        user: { userId: user.userId },
        isOwned: true,
      },
      relations: ['user'],
    });

    if (!nft) {
      throw new BadRequestException('NFT not found in your portfolio');
    }

    return nft;
  }

  async searchNfts(auth: AuthData, query: string): Promise<Nft[]> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return this.nftRepository
      .createQueryBuilder('nft')
      .where('nft.user.userId = :userId', { userId: user.userId })
      .andWhere('nft.isOwned = :isOwned', { isOwned: true })
      .andWhere(
        '(nft.name ILIKE :query OR nft.collectionName ILIKE :query OR nft.description ILIKE :query)',
        { query: `%${query}%` },
      )
      .getMany();
  }
}
