import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NftController } from './nft.controller';
import { NftService } from './nft.service';
import { NftRepository } from '../repositories/nft.repository';
import { UserRepository } from '../repositories/users.repository';
import { Nft } from '../entities/nft.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Nft, User]),
  ],
  controllers: [NftController],
  providers: [
    NftService,
    NftRepository,
    UserRepository,
  ],
  exports: [NftService],
})
export class NftModule {}
