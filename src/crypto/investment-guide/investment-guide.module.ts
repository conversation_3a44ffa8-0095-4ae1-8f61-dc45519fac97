import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InvestmentGuideService } from './investment-guide.service';
import { UserRepository } from '../repositories/users.repository';
import { InvestmentTemplateRepository } from '../repositories/investment-template.repository';
import { QuidaxModule } from '@app/quidax';
import { User } from '../entities/user.entity';
import { InvestmentTemplate } from '../entities/investment-template.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, InvestmentTemplate]),
    QuidaxModule,
  ],
  providers: [
    InvestmentGuideService,
    UserRepository,
    InvestmentTemplateRepository,
  ],
  exports: [InvestmentGuideService],
})
export class InvestmentGuideModule {}
