import { Injectable, Logger } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { InvestmentTemplateRepository } from '../repositories/investment-template.repository';
import { QuidaxService } from '@app/quidax';
import { randomUUID } from 'crypto';
import { RiskProfile } from '../entities/user.entity';

@Injectable()
export class InvestmentGuideService {
  private readonly logger = new Logger(InvestmentGuideService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly templateRepository: InvestmentTemplateRepository,
    private readonly quidaxService: QuidaxService,
  ) {}

  async getRiskAssessment(userId: string) {
    const questions = [
      {
        id: 1,
        question: 'How long do you plan to hold your investments?',
        options: [
          { id: 'a', text: 'Less than 1 year', score: 1 },
          { id: 'b', text: '1-3 years', score: 2 },
          { id: 'c', text: '3-5 years', score: 3 },
          { id: 'd', text: 'More than 5 years', score: 4 },
        ],
      },
      // More questions...
    ];

    return { questions };
  }

  async processRiskAssessment(userId: string, answers: any[]) {
    const user = await this.userRepository.getUserByUserId(userId);

    // Calculate risk score
    const riskScore = this.calculateRiskScore(answers);

    // Determine risk profile based on score
    const riskProfile = this.getRiskProfileFromScore(riskScore);

    // Save risk profile
    await this.userRepository.update(
      { id: user.id },
      { riskProfile, riskScore },
    );

    // Get investment templates based on risk profile
    const templates =
      await this.templateRepository.findByRiskProfile(riskScore);

    return {
      riskScore,
      riskProfile,
      recommendedTemplates: templates,
    };
  }

  async applyInvestmentTemplate(
    userId: string,
    templateId: string,
    amount: number,
  ) {
    const user = await this.userRepository.getUserByUserId(userId);
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
    });

    // Create orders based on template allocation
    const orders = [];
    for (const allocation of template.allocations) {
      const orderAmount = amount * (allocation.percentage / 100);

      // Create order
      const reference = 'cp_template_' + randomUUID();
      const order = await this.quidaxService.createOrder(user.id, {
        market: allocation.currency + 'ngn',
        side: 'buy',
        volume: orderAmount.toString(),
        ord_type: 'market',
        reference,
      });

      orders.push(order);
    }

    return {
      template,
      orders,
      totalAmount: amount,
    };
  }

  private calculateRiskScore(answers: any[]): number {
    // Implementation to calculate risk score based on answers
    let score = 0;

    for (const answer of answers) {
      // Each answer contributes to the risk score
      // Higher values indicate higher risk tolerance
      score += parseInt(answer.value) || 0;
    }

    return score;
  }

  private getRiskProfileFromScore(score: number): RiskProfile {
    if (score <= 20) return RiskProfile.CONSERVATIVE;
    if (score <= 40) return RiskProfile.MODERATE;
    return RiskProfile.AGGRESSIVE;
  }

  private getRiskProfileLabel(score: number): string {
    if (score <= 20) return 'Conservative';
    if (score <= 40) return 'Moderate';
    return 'Aggressive';
  }
}
