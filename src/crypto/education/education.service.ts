import { Injectable } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { TransactionRepository } from '../repositories/transaction.repository';

@Injectable()
export class EducationService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly transactionRepository: TransactionRepository,
  ) {}

  async getEducationalContent(category?: string, level?: string) {
    // Mock educational content
    const allContent = [
      {
        id: '1',
        title: 'Introduction to Cryptocurrency',
        category: 'basics',
        level: 'beginner',
        content: 'Learn the fundamentals of cryptocurrency...',
        duration: '10 min',
      },
      {
        id: '2',
        title: 'Understanding Blockchain Technology',
        category: 'technology',
        level: 'intermediate',
        content: 'Deep dive into blockchain technology...',
        duration: '15 min',
      },
      {
        id: '3',
        title: 'Advanced Trading Strategies',
        category: 'trading',
        level: 'advanced',
        content: 'Learn advanced trading techniques...',
        duration: '20 min',
      },
    ];

    let filteredContent = allContent;

    if (category) {
      filteredContent = filteredContent.filter(
        (content) => content.category === category,
      );
    }

    if (level) {
      filteredContent = filteredContent.filter(
        (content) => content.level === level,
      );
    }

    return filteredContent;
  }

  async getRecommendedContent(userId: string) {
    const user = await this.userRepository.getUserByUserId(userId);
    const userTransactions = await this.getUserTransactionHistory(user.id);

    // Determine user's experience level based on transaction history
    const experienceLevel = this.determineExperienceLevel(userTransactions);

    // Get content based on user's experience level
    return this.getEducationalContent(undefined, experienceLevel);
  }

  async getContentById(id: string) {
    const allContent = await this.getEducationalContent();
    return allContent.find((content) => content.id === id);
  }

  async getUserProgress(userId: string) {
    // Mock user progress data
    return {
      userId,
      completedLessons: 5,
      totalLessons: 20,
      currentLevel: 'intermediate',
      achievements: ['First Trade', 'Portfolio Builder'],
      progressPercentage: 25,
    };
  }

  private async getUserTransactionHistory(userId: string) {
    return this.transactionRepository.find({
      where: { user: { id: userId } },
      relations: ['user'],
    });
  }

  private determineExperienceLevel(transactions) {
    const count = transactions.length;

    if (count < 5) return 'beginner';
    if (count < 20) return 'intermediate';
    return 'advanced';
  }
}
