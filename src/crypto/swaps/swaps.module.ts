import { Modu<PERSON> } from '@nestjs/common';
import { SwapsService } from './swaps.service';
import { SwapsController } from './swaps.controller';
import { SwapTransactionRepository } from '../repositories/swap-transaction.repository';
import { SwapQuotationRepository } from '../repositories/swap-quotation.repository';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxModule } from '@app/quidax';
import { RabbitmqModule } from '@crednet/utils';
import { WebhookModule } from '../../utils/webhook/webhook.module';
import { SwapsWebhookConsumer } from './swaps.webhook.consumer';
import {
  SwapTransactionCompletedEventHandler,
  SwapTransactionFailedEventHandler,
  SwapTransactionReversedEventHandler,
} from './swaps.event-handlers';
import { SwapsCron } from './swaps.cron';

@Module({
  imports: [QuidaxModule, RabbitmqModule, WebhookModule],
  controllers: [SwapsController],
  providers: [
    SwapsService,
    SwapTransactionRepository,
    SwapQuotationRepository,
    UserRepository,
    SwapsWebhookConsumer,
    SwapTransactionCompletedEventHandler,
    SwapTransactionFailedEventHandler,
    SwapTransactionReversedEventHandler,
    SwapsCron,
  ],
})
export class SwapsModule {}
