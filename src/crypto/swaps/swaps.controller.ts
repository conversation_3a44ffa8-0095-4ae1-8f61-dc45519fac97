import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  Put,
} from '@nestjs/common';
import { SwapsService } from './swaps.service';
import { AuthData, JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CreateSwapDto, GetSwapsByUserIdDto } from '../dtos/swaps.dto';

@Controller('swaps')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('swaps')
export class SwapsController {
  constructor(private readonly swapsService: SwapsService) {}

  @Post('quotations')
  async createSwapQuotation(
    @GetAuthData() auth: AuthData,
    @Body() createSwapDto: CreateSwapDto,
  ) {
    return this.swapsService.createSwap(createSwapDto, auth);
  }

  @Get('quotations')
  async getAllSwapQuotations(@GetAuthData() auth: AuthData) {
    return this.swapsService.getAllSwapQuotations(auth);
  }

  @Get('quotations/history')
  async getSwapQuotationHistory(
    @GetAuthData() auth: AuthData,
    @Query() query: GetSwapsByUserIdDto,
  ) {
    const { startDate, endDate, page, limit } = query;
    return await this.swapsService.getAllSwapQuotationsByUserId(
      auth,
      new Date(startDate),
      new Date(endDate),
      page,
      limit,
    );
  }

  @Get('quotations/:quotationId')
  async getSwapQuotation(
    @GetAuthData() auth: AuthData,
    @Param('quotationId') quotationId: string,
  ) {
    return this.swapsService.getSwapQuotation(auth, quotationId);
  }

  @Get('quotations/:quotationId/confirmed')
  async getSwapQuotationFromDatabase(
    @Param('quotationId') quotationId: string,
  ) {
    return this.swapsService.getSwapConfirmedQuotation(quotationId);
  }

  @Put('quotations/:quotationId/refresh')
  async refreshSwapQuotation(
    @GetAuthData() auth: AuthData,
    @Param('quotationId') quotationId: string,
    @Body() createSwapDto: CreateSwapDto,
  ) {
    return this.swapsService.refreshSwapQuotation(
      auth,
      quotationId,
      createSwapDto,
    );
  }

  @Post('quotations/temporary')
  async createTemporarySwapQuotation(
    @GetAuthData() auth: AuthData,
    @Body() createSwapDto: CreateSwapDto,
  ) {
    return this.swapsService.temporarySwapQuotation(auth, createSwapDto);
  }

  @Post('quotations/:quotationId/confirm')
  async confirmSwapTransaction(
    @GetAuthData() auth: AuthData,
    @Param('quotationId') quotationId: string,
  ) {
    return this.swapsService.confirmSwapTransaction(auth, quotationId);
  }

  @Get('transactions')
  async getAllSwapTransactions(
    @GetAuthData() auth: AuthData,
    @Query() query: GetSwapsByUserIdDto,
  ) {
    const { startDate, endDate, page, limit } = query;
    return await this.swapsService.getAllSwapTransactions(
      auth,
      new Date(startDate),
      new Date(endDate),
      page,
      limit,
    );
  }

  @Get('transactions/:transactionId')
  async getSwapTransaction(@Param('transactionId') transactionId: string) {
    return await this.swapsService.getSwapTransaction(transactionId);
  }

  @Put('transactions/:transactionId/verify')
  async verifySwapTransaction(@Param('transactionId') transactionId: string) {
    return await this.swapsService.verifySwapTransaction(transactionId);
  }
}
