import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { QuidaxService } from '@app/quidax';
import { randomUUID } from 'crypto';
import { UserRepository } from '../repositories/users.repository';
import { TransactionRepository } from '../repositories/transaction.repository';

@Injectable()
export class RecurringBuysService {
  constructor(
    private readonly quidaxService: QuidaxService,
    private readonly userRepository: UserRepository,
    private readonly transactionRepository: TransactionRepository,
  ) {}

  async createRecurringBuy(userId: string, recurringBuyDto) {
    const user = await this.userRepository.getUserByUserId(userId);

    // Mock implementation - in a real app this would save to a RecurringBuy entity
    const recurringBuy = {
      id: randomUUID(),
      user,
      amount: recurringBuyDto.amount,
      currency: recurringBuyDto.currency,
      frequency: recurringBuyDto.frequency,
      nextExecutionDate: this.calculateNextExecutionDate(
        recurringBuyDto.frequency,
      ),
      isActive: true,
      createdAt: new Date(),
    };

    return recurringBuy;
  }

  @Cron(CronExpression.EVERY_HOUR)
  async executeRecurringBuys() {
    // Mock implementation - in a real app this would query the RecurringBuy repository
    const pendingBuys: any[] = [];

    for (const buy of pendingBuys) {
      try {
        // Execute the buy order
        const reference = 'cp_rec_buy_' + randomUUID();
        await this.quidaxService.createOrder(buy.user.id, {
          market: buy.currency + 'ngn',
          side: 'buy',
          volume: buy.amount,
          ord_type: 'market',
          reference,
        });

        // Update next execution date
        buy.lastExecutionDate = new Date();
        buy.nextExecutionDate = this.calculateNextExecutionDate(
          buy.frequency,
          buy.lastExecutionDate,
        );
        // In a real app, this would save the updated buy to the repository
      } catch (error) {
        console.error(
          `Failed to execute recurring buy for user ${buy.user.id}:`,
          error,
        );
      }
    }
  }

  private calculateNextExecutionDate(frequency, fromDate = new Date()) {
    // Implementation to calculate next execution date based on frequency
  }
}
