import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RecurringBuysService } from './recurring-buys.service';
import { RecurringBuysController } from './recurring-buys.controller';
import { DcaStrategyRepository } from '../repositories/dca-strategy.repository';
import { UserRepository } from '../repositories/users.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { CurrencyRepository } from '../repositories/currency.repository';
import { QuidaxModule } from '@app/quidax';
import { DcaStrategy } from '../entities/dca-strategy.entity';
import { User } from '../entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([DcaStrategy, User]), QuidaxModule],
  controllers: [RecurringBuysController],
  providers: [
    RecurringBuysService,
    DcaStrategyRepository,
    UserRepository,
    TransactionRepository,
    WalletRepository,
    CurrencyRepository,
  ],
  exports: [RecurringBuysService],
})
export class RecurringModule {}
