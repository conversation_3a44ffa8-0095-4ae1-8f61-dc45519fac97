import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { RecurringBuysService } from './recurring-buys.service';
import { CreateRecurringBuyDto } from './dtos/recurring-buy.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';

@ApiTags('Recurring Buys')
@Controller('recurring-buys')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
export class RecurringBuysController {
  constructor(private readonly recurringBuysService: RecurringBuysService) {}

  @Post()
  createRecurringBuy(
    @Body() createRecurringBuyDto: CreateRecurringBuyDto,
    @GetAuthData() auth: AuthData,
  ) {
    return this.recurringBuysService.createRecurringBuy(
      auth.id.toString(),
      createRecurringBuyDto,
    );
  }

  @Get()
  getUserRecurringBuys(@GetAuthData() auth: AuthData) {
    // Mock implementation since the service method doesn't exist yet
    return { recurringBuys: [] };
  }

  @Get(':id')
  getRecurringBuy(@Param('id') id: string, @GetAuthData() auth: AuthData) {
    // Mock implementation since the service method doesn't exist yet
    return { id, userId: auth.id.toString() };
  }

  @Delete(':id')
  cancelRecurringBuy(@Param('id') id: string, @GetAuthData() auth: AuthData) {
    // Mock implementation since the service method doesn't exist yet
    return { message: 'Recurring buy cancelled', id };
  }
}
