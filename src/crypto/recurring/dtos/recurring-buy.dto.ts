import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum, IsOptional } from 'class-validator';

export enum RecurringBuyFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

export class CreateRecurringBuyDto {
  @ApiProperty({ description: 'Currency to buy' })
  @IsString()
  currency: string;

  @ApiProperty({ description: 'Amount to buy each time' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Frequency of the recurring buy', enum: RecurringBuyFrequency })
  @IsEnum(RecurringBuyFrequency)
  frequency: RecurringBuyFrequency;

  @ApiProperty({ description: 'Start date for recurring buys', required: false })
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ description: 'End date for recurring buys', required: false })
  @IsOptional()
  endDate?: Date;
}
