import { Injectable, Logger } from '@nestjs/common';
import { WebhookEventHandler } from '../../utils/webhook/webhook-event.handler';
import { OrdersService } from './orders.service';
import { TradeRepository } from '../repositories/trades.repository';

/**
 * Base handler for order events
 */
@Injectable()
export abstract class BaseOrderEventHandler implements WebhookEventHandler {
  protected readonly logger = new Logger(BaseOrderEventHandler.name);

  constructor(protected readonly ordersService: OrdersService) {}

  abstract handle(data: any): Promise<void>;
}

/**
 * Handler for order done events
 */
@Injectable()
export class OrderDoneEventHandler extends BaseOrderEventHandler {
  constructor(
    protected readonly ordersService: OrdersService,
    private readonly tradeRepository: TradeRepository,
  ) {
    super(ordersService);
  }

  async handle(data: any): Promise<void> {
    // Get the order
    const order = await this.ordersService.getOrder(data.id);

    // Update the order
    await this.ordersService.updateOrderByReference(data.reference, data);

    // Create trades
    if (data.trades && data.trades.length > 0) {
      for (const trade of data.trades) {
        await this.tradeRepository.save({
          id: trade.id,
          market_id: trade.market.id,
          market_base_unit: trade.market.base_unit,
          market_quote_unit: trade.market.quote_unit,
          price_amount: trade.price.amount,
          price_unit: trade.price.unit,
          volume_amount: trade.volume.amount,
          volume_unit: trade.volume.unit,
          total_amount: trade.total.amount,
          total_unit: trade.total.unit,
          order,
        });
      }
    }

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(
      `Order completed for user ${order.user.userId}, order ID: ${order.id}`,
    );
  }
}

/**
 * Handler for order cancelled events
 */
@Injectable()
export class OrderCancelledEventHandler extends BaseOrderEventHandler {
  async handle(data: any): Promise<void> {
    // Update the order
    const order = await this.ordersService.updateOrderByReference(
      data.reference,
      data,
    );

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(
      `Order cancelled for user ${order.user.userId}, order ID: ${order.id}`,
    );
  }
}
