import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
  Put,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { AuthData, JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { CreateOrderDto, GetOrdersByUserIdDto } from '../dtos/orders.dto';

@Controller('orders')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  async createOrder(
    @GetAuthData() auth: AuthData,
    @Body() createOrderDto: CreateOrderDto,
  ) {
    return this.ordersService.createOrder(auth, createOrderDto);
  }

  @Get(':orderId')
  async getOrder(@Param('orderId') orderId: string) {
    return await this.ordersService.getOrder(orderId);
  }

  @Get()
  async getOrdersByUserId(
    @GetAuthData() auth: AuthData,
    @Query() query: GetOrdersByUserIdDto,
  ) {
    const { startDate, endDate, page, limit } = query;
    return await this.ordersService.getOrdersByUserId(
      auth.id.toString(),
      new Date(startDate),
      new Date(endDate),
      page,
      limit,
    );
  }

  @Put(':orderId')
  async cancelOrder(@Param('orderId') orderId: string, @Body() auth: AuthData) {
    return await this.ordersService.cancelOrder(auth, orderId);
  }
}
