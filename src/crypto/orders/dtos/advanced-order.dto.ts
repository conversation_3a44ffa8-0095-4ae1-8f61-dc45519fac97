import { <PERSON><PERSON>num, IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum AdvancedOrderType {
  STOP_LOSS = 'stop_loss',
  TAKE_PROFIT = 'take_profit',
  TRAILING_STOP = 'trailing_stop',
}

export class CreateAdvancedOrderDto {
  @ApiProperty({ description: 'Base currency (e.g., BTC)' })
  @IsString()
  @IsNotEmpty()
  baseCurrency: string;

  @ApiProperty({ description: 'Quote currency (e.g., NGN)' })
  @IsString()
  @IsNotEmpty()
  quoteCurrency: string;

  @ApiProperty({ description: 'Order side (buy/sell)' })
  @IsEnum(['buy', 'sell'])
  side: string;

  @ApiProperty({ description: 'Volume to trade' })
  @IsNumber()
  volume: number;

  @ApiProperty({ enum: AdvancedOrderType, description: 'Type of advanced order' })
  @IsEnum(AdvancedOrderType)
  orderType: AdvancedOrderType;

  @ApiProperty({ description: 'Price at which to trigger the order' })
  @IsNumber()
  triggerPrice: number;

  @ApiProperty({ description: 'Trailing percentage for trailing stop orders', required: false })
  @IsOptional()
  @IsNumber()
  trailingPercentage?: number;
}