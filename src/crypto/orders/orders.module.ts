import { Module } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrdersController } from './orders.controller';
import { QuidaxModule } from '@app/quidax';
import { UserRepository } from '../repositories/users.repository';
import { RabbitmqModule } from '@crednet/utils';
import { TradeRepository } from '../repositories/trades.repository';
import { OrdersWebhookConsumer } from './orders.webhook.consumer';
import { OrderRepository } from '../repositories/orders.repository';
import { WebhookModule } from '../../utils/webhook/webhook.module';
import { KycModule } from '../kyc/kyc.module';
import {
  OrderDoneEventHandler,
  OrderCancelledEventHandler,
} from './orders.event-handlers';
import { OrdersCron } from './orders.cron';

@Module({
  imports: [QuidaxModule, RabbitmqModule, WebhookModule, KycModule],
  controllers: [OrdersController],
  providers: [
    OrdersService,
    UserRepository,
    TradeRepository,
    OrdersWebhookConsumer,
    OrderRepository,
    OrderDoneEventHandler,
    OrderCancelledEventHandler,
    OrdersCron,
  ],
})
export class OrdersModule {}
