import { Injectable, Logger } from '@nestjs/common';
import { WebhookEventHandler } from '../../utils/webhook/webhook-event.handler';
import { DepositsService } from './deposits.service';

/**
 * Base handler for deposit events
 */
@Injectable()
export abstract class BaseDepositEventHandler implements WebhookEventHandler {
  protected readonly logger = new Logger(BaseDepositEventHandler.name);

  constructor(protected readonly depositService: DepositsService) {}

  abstract handle(data: any): Promise<void>;
}

/**
 * Handler for deposit completed events
 */
@Injectable()
export class DepositCompletedEventHandler extends BaseDepositEventHandler {
  async handle(data: any): Promise<void> {
    const deposit = await this.depositService.createDeposit(data);

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(`Deposit completed for user ${deposit.user.userId}`);
  }
}

/**
 * Handler for deposit failed AML events
 */
@Injectable()
export class DepositFailedAmlEventHandler extends BaseDepositEventHandler {
  async handle(data: any): Promise<void> {
    const deposit = await this.depositService.createDeposit(data);

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(`Deposit failed AML check for user ${deposit.user.userId}`);
  }
}

/**
 * Handler for deposit on hold events
 */
@Injectable()
export class DepositOnHoldEventHandler extends BaseDepositEventHandler {
  async handle(data: any): Promise<void> {
    const deposit = await this.depositService.createDeposit(data);

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(`Deposit on hold for user ${deposit.user.userId}`);
  }
}

/**
 * Handler for deposit successful events
 */
@Injectable()
export class DepositSuccessfulEventHandler extends BaseDepositEventHandler {
  async handle(data: any): Promise<void> {
    const deposit = await this.depositService.createDeposit(data);

    // TODO: Send notification to user when notification service is implemented
    this.logger.log(`Deposit successful for user ${deposit.user.userId}`);
  }
}
