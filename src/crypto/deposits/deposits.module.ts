import { Module } from '@nestjs/common';
import { DepositsService } from './deposits.service';
import { DepositsController } from './deposits.controller';
import { UserRepository } from '../repositories/users.repository';
import { WalletRepository } from '../repositories/wallet.repository';
import { DepositsWebhookConsumer } from './deposits.webhook.consumer';
import { DepositRepository } from '../repositories/deposits.repository';
import { CurrencyRepository } from '../repositories/currency.repository';
import { WebhookModule } from '../../utils/webhook/webhook.module';
import {
  DepositCompletedEventHandler,
  DepositFailed<PERSON>mlEventHandler,
  DepositOnHoldEventHandler,
  DepositSuccessfulEventHandler,
} from './deposits.event-handlers';

@Module({
  imports: [WebhookModule],
  controllers: [DepositsController],
  providers: [
    DepositsService,
    UserRepository,
    WalletRepository,
    DepositsWebhookConsumer,
    DepositRepository,
    CurrencyRepository,
    DepositCompletedEventHandler,
    DepositFailedAmlEventHandler,
    DepositOnHoldEventHandler,
    DepositSuccessfulEventHandler,
  ],
})
export class DepositsModule {}
