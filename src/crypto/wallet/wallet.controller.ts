import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { JwtAuthGuard, GetAuthData, AuthData } from '@crednet/authmanager';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Currency } from '../entities/currency.entity';
import { Wallet } from '../entities/wallet.entity';

@Controller('wallets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('wallets')
export class WalletController {
  constructor(private readonly walletService: WalletService) {}
  @Get('/')
  async fetchUserWallets(@GetAuthData() auth: AuthData): Promise<Wallet[]> {
    return this.walletService.getAllUserActiveWallets(auth.id.toString());
  }

  @Get('/currencies')
  @ApiQuery({
    name: 'currencyCode',
    required: false,
    description: 'Filter currencies by code',
  })
  async findAllCurrencies(
    @Query('currencyCode') currencyCode?: string,
  ): Promise<Currency[]> {
    return this.walletService.findAllCurrencies(currencyCode);
  }

  @Get('/:id')
  async getWalletDetails(@Param('id') id: string): Promise<Wallet> {
    return this.walletService.getUserWallet(id);
  }
}
